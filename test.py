from NodeRAG import NodeConfig, NodeSearch
import pandas as pd
import csv

# Load configuration from the main folder
config = NodeConfig.from_main_folder(fr"/tmp2/b11705045/AI/Final_project")

# Initialize search engine
search = NodeSearch(config)

with open('baseline_prompt.txt', 'r') as file:
    prompt = file.read()

with open("record.txt", 'w') as file:
    with open("final_project_query.csv", 'r') as query_file:
        df = pd.read_csv(query_file)
        for i, row in df.iterrows():
            ans = search.answer(f'{prompt}\n請分析廣告內容：\n{row["Question"]}')
            print(ans.response)
            break
        # csv_reader = csv.reader(file)
        # for i, row in enumerate(csv_reader):
        #     if i == 0:
        #         continue

        #     ans = search.answer(f'{prompt}\n請分析廣告內容：\n{row[1]}')
        #     # Response: the generated answer to your question
        #     print(ans.response)

        #     # Number of tokens in the answer
        #     print(ans.response_tokens)

        #     # Retrieval info: the context used to generate the answer
        #     print(ans.retrieval_info)

        #     # Number of tokens in the retrieval context
        #     print(ans.retrieval_tokens)

        #     break